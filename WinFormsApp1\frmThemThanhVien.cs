using Microsoft.EntityFrameworkCore;
using Models.HandleData;
using Models.Models;

namespace WinFormsApp1
{
    public partial class frmThemThanhVien : Form
    {
        private int deTaiId;

        public int SelectedCanBoId { get; private set; }
        public VaiTroThamGiaEnum SelectedVaiTro { get; private set; }

        public frmThemThanhVien(int deTaiId)
        {
            this.deTaiId = deTaiId;
            InitializeComponent();
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                using (var context = new DAContext())
                {
                    // Kiểm tra đề tài có tồn tại không
                    var deTaiExists = await context.DeTai.AnyAsync(dt => dt.MaDeTai == deTaiId);
                    if (!deTaiExists)
                    {
                        MessageBox.Show("Đề tài không tồn tại!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        this.DialogResult = DialogResult.Cancel;
                        this.Close();
                        return;
                    }

                    // Load danh sách cán bộ chưa tham gia đề tài này
                    var existingCanBoIds = await context.VaiTroThamGia
                        .Where(vt => vt.MaDeTai == deTaiId)
                        .Select(vt => vt.MaCanBo)
                        .ToListAsync();

                    var availableCanBo = await context.CanBo
                        .Where(cb => !existingCanBoIds.Contains(cb.MaCanBo))
                        .OrderBy(cb => cb.HoTen)
                        .ToListAsync();

                    cmbCanBo.Items.Clear();
                    foreach (var canBo in availableCanBo)
                    {
                        cmbCanBo.Items.Add(new ComboBoxItem { Text = canBo.HoTen ?? "N/A", Value = canBo.MaCanBo });
                    }

                    if (cmbCanBo.Items.Count > 0)
                    {
                        cmbCanBo.SelectedIndex = 0;
                    }
                    else
                    {
                        MessageBox.Show("Không có cán bộ nào khả dụng để thêm vào đề tài!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        this.DialogResult = DialogResult.Cancel;
                        this.Close();
                        return;
                    }

                    // Setup vai trò combo box
                    cmbVaiTro.Items.Clear();
                    cmbVaiTro.Items.Add("Chủ nhiệm");
                    cmbVaiTro.Items.Add("Tham gia");
                    cmbVaiTro.SelectedIndex = 1; // Mặc định là "Tham gia"
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }

        private async void BtnLuu_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var selectedItem = (ComboBoxItem)cmbCanBo.SelectedItem;
                SelectedCanBoId = selectedItem.Value;
                SelectedVaiTro = cmbVaiTro.SelectedIndex == 0 ? VaiTroThamGiaEnum.ChuNhiem : VaiTroThamGiaEnum.ThamGia;

                // Kiểm tra lại dữ liệu trước khi đóng form
                using (var context = new DAContext())
                {
                    // Kiểm tra đề tài và cán bộ vẫn tồn tại
                    var deTaiExists = await context.DeTai.AnyAsync(dt => dt.MaDeTai == deTaiId);
                    var canBoExists = await context.CanBo.AnyAsync(cb => cb.MaCanBo == SelectedCanBoId);

                    if (!deTaiExists)
                    {
                        MessageBox.Show("Đề tài không tồn tại!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    if (!canBoExists)
                    {
                        MessageBox.Show("Cán bộ không tồn tại!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // Kiểm tra cán bộ chưa tham gia đề tài này
                    var alreadyExists = await context.VaiTroThamGia
                        .AnyAsync(vt => vt.MaDeTai == deTaiId && vt.MaCanBo == SelectedCanBoId);

                    if (alreadyExists)
                    {
                        MessageBox.Show("Cán bộ này đã tham gia đề tài!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // Nếu vai trò là chủ nhiệm, kiểm tra đề tài chưa có chủ nhiệm
                    if (SelectedVaiTro == VaiTroThamGiaEnum.ChuNhiem)
                    {
                        var hasExistingChuNhiem = await context.VaiTroThamGia
                            .AnyAsync(vt => vt.MaDeTai == deTaiId && vt.VaiTro == VaiTroThamGiaEnum.ChuNhiem);

                        if (hasExistingChuNhiem)
                        {
                            MessageBox.Show("Mỗi đề tài chỉ có 1 chủ nhiệm!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }
                    }
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi kiểm tra dữ liệu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (cmbCanBo.SelectedItem == null)
            {
                MessageBox.Show("Vui lòng chọn cán bộ!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCanBo.Focus();
                return false;
            }

            if (cmbVaiTro.SelectedIndex < 0)
            {
                MessageBox.Show("Vui lòng chọn vai trò!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbVaiTro.Focus();
                return false;
            }

            return true;
        }

        private void BtnHuy_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }

    public class ComboBoxItem
    {
        public string Text { get; set; } = string.Empty;
        public int Value { get; set; }

        public override string ToString()
        {
            return Text;
        }
    }
}
